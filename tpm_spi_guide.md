# TPM SPI实现步骤指南

## TPM SPI实现的完整流程

### 第一步：硬件连接
TPM芯片通过4根SPI信号线连接到主控制器：
- **CLK** - 时钟信号
- **MOSI** - 主控发送数据
- **MISO** - 主控接收数据
- **CS** - 片选信号
- **IRQ** - 中断信号（可选）

### 第二步：设备树配置
在设备树中定义SPI控制器和TPM设备，每一行的详细解释：

**文件路径**：`arch/loongarch/boot/dts/loongson/your_board.dts`

```dts
&spi0 {                                    // 引用SPI控制器0
    tpm@0 {                               // TPM设备节点，@0表示片选0
        compatible = "tcg,tpm_tis-spi";   // 驱动匹配字符串
        reg = <0>;                        // 片选号：0表示CS0
        spi-max-frequency = <10000000>;   // 最大SPI频率：10MHz
        interrupt-parent = <&gpio1>;      // 中断控制器：GPIO控制器1
        interrupts = <12 IRQ_TYPE_LEVEL_LOW>; // 中断：GPIO1_12，低电平触发
    };
};
```

#### 每行配置详解：

**`&spi0`** - SPI控制器选择
- `spi0` = 第一个SPI控制器（从0开始编号）
- `spi1` = 第二个SPI控制器
- 选择原因：根据硬件连接，TPM连接到哪个SPI控制器就选择哪个
- 查看方法：分析设备树文件中的SPI控制器定义

**`tpm@0`** - 设备节点名称
- `tpm` = 设备类型名称（可以是任意名称）
- `@0` = 单元地址，对应reg属性的值
- 为什么是@0：因为TPM使用片选0（CS0）
- 如果用CS1则写成 `tpm@1`

**`compatible = "tcg,tpm_tis-spi"`** - 驱动匹配
- `tcg` = 厂商前缀（Trusted Computing Group）
- `tpm_tis-spi` = 具体的驱动名称
- 作用：内核根据这个字符串找到对应的驱动程序
- 其他选项：`"infineon,slb9670"`, `"st,st33htpm-spi"`

**`reg = <0>`** - 片选配置
- `0` = 使用片选0（CS0）
- `1` = 使用片选1（CS1）
- 选择原因：根据硬件设计，TPM的CS引脚连接到哪个片选
- 必须与节点名@后的数字一致

**`spi-max-frequency = <10000000>`** - 频率设置
- `10000000` = 10MHz（单位：Hz）
- 为什么是10MHz：大多数TPM芯片支持的标准频率
- 可选值：`5000000`(5MHz), `20000000`(20MHz)
- 过高可能导致通信错误，过低影响性能

**`interrupt-parent = <&gpio1>`** - 中断控制器
- `&gpio1` = 引用GPIO控制器1
- 为什么是gpio1：根据硬件连接，TPM的IRQ引脚连接到哪个GPIO控制器
- 其他选项：`&gpio0`, `&gpio2` 等
- 查看方法：查看硬件原理图确定连接

**`interrupts = <12 IRQ_TYPE_LEVEL_LOW>`** - 中断配置
- `12` = GPIO引脚号（GPIO1_12）
- `IRQ_TYPE_LEVEL_LOW` = 低电平触发
- 为什么是引脚12：根据硬件连接确定
- 其他触发类型：
  - `IRQ_TYPE_LEVEL_HIGH` - 高电平触发
  - `IRQ_TYPE_EDGE_FALLING` - 下降沿触发
  - `IRQ_TYPE_EDGE_RISING` - 上升沿触发

### 第三步：驱动注册
系统启动时，`tpm_tis_spi.c` 驱动注册到SPI总线：
- 定义支持的设备ID表
- 注册probe和remove函数
- 绑定到匹配的设备树节点

### 第四步：设备探测
当SPI总线发现匹配的TPM设备时：
- 调用 `tpm_tis_spi_probe()` 函数
- 分配SPI物理层结构体
- 配置SPI通信参数
- 调用 `tpm_tis_core_init()` 进行TPM初始化

### 第五步：SPI通信层实现
实现TPM专用的SPI通信协议：
- **读操作**：发送读命令 + 地址，接收数据
- **写操作**：发送写命令 + 地址 + 数据
- **等待处理**：TPM忙时轮询等待就绪

### 第六步：集成到TPM核心
SPI驱动通过操作函数表集成到TPM核心：
- `read_bytes` - 读取字节数据
- `write_bytes` - 写入字节数据
- `read16/read32` - 读取16/32位数据
- `write32` - 写入32位数据

## SPI在TPM项目中的具体实现

### 实现架构
```
应用层 (/dev/tpm0)
    ↓
TPM核心 (tpm-chip.c)
    ↓
TIS协议层 (tpm_tis_core.c)
    ↓
SPI物理层 (tpm_tis_spi.c)
    ↓
Linux SPI子系统
    ↓
硬件SPI控制器
```

### 关键文件和作用

| 文件 | 作用 |
|------|------|
| `tpm_tis_spi.c` | SPI物理层驱动，实现SPI通信 |
| `tpm_tis_core.c` | TIS协议核心，调用物理层接口 |
| `tpm-chip.c` | TPM设备管理，创建字符设备 |
| 设备树文件 | 硬件配置，定义SPI参数 |

### SPI通信协议
TPM SPI使用特殊的传输格式：
```
[控制字节][地址高][地址低][长度-1][数据...]
```
- 控制字节：区分读写操作
- 地址：TPM寄存器地址
- 长度：传输数据长度
- 数据：实际传输的数据

### 等待机制
TPM可能不立即响应，需要轮询：
1. 发送命令后检查响应
2. 如果TPM忙，发送空字节查询
3. 重复查询直到TPM就绪
4. 继续数据传输

## 配置步骤详解

### 步骤1：内核配置
启用必要的内核选项：

**文件路径**：`.config` 或通过 `make menuconfig` 配置

```
CONFIG_TCG_TIS_SPI=y        # TPM SPI驱动
CONFIG_SPI=y                # SPI子系统
CONFIG_SPI_MASTER=y         # SPI主控制器
```

### 步骤2：设备树配置

**文件路径**：`arch/loongarch/boot/dts/loongson/your_board.dts`

```dts
&spi0 {                                    // 选择SPI控制器0
    status = "okay";                       // 启用SPI控制器
    tpm@0 {                               // TPM设备，使用片选0
        compatible = "tcg,tpm_tis-spi";   // 匹配TPM SPI驱动
        reg = <0>;                        // 片选号0
        spi-max-frequency = <10000000>;   // 10MHz频率
        interrupt-parent = <&gpio1>;      // 使用GPIO1控制器
        interrupts = <12 IRQ_TYPE_LEVEL_LOW>; // GPIO1_12，低电平触发
    };
};
```

#### 如何确定这些配置值：

**确定SPI控制器编号**：
- 查看项目中的设备树文件：`arch/loongarch/boot/dts/loongson/loongson64_2p500.dtsi`
- 分析SPI控制器定义：spi1、spi2、spi3的基址和状态
- 选择原则：优先选择已在板级文件中启用的SPI控制器

**确定片选号**：
- 查看硬件原理图，确定TPM的CS引脚连接
- 通常TPM作为唯一设备使用CS0（reg = <0>）
- 片选号必须与设备节点名称中的@后数字一致

**确定GPIO和中断**：
- 查看硬件原理图，确定TPM的IRQ引脚连接到哪个GPIO
- 查看设备树中的GPIO控制器定义
- 确定中断触发类型（查看TPM芯片数据手册）

**基于项目代码分析**：
```bash
# 查看设备树中的SPI定义
grep -r "spi@" arch/loongarch/boot/dts/loongson/

# 查看GPIO控制器定义
grep -r "gpio" arch/loongarch/boot/dts/loongson/ | grep compatible

# 查看中断控制器定义
grep -r "interrupt-controller" arch/loongarch/boot/dts/loongson/
```

### 步骤3：驱动加载流程
1. **模块初始化**：`tpm_tis_spi_driver_init()`
2. **设备匹配**：根据compatible字符串匹配
3. **设备探测**：调用probe函数
4. **资源分配**：分配内存和配置SPI
5. **TPM初始化**：调用TIS核心初始化
6. **设备注册**：创建/dev/tpm0设备文件

### 步骤4：SPI通信实现
- **发送命令**：构造SPI传输帧
- **接收响应**：读取TPM返回数据
- **错误处理**：处理通信超时和错误
- **中断支持**：可选的中断驱动模式

## 验证和调试

### 验证SPI配置是否成功

**注意**：以下验证方法需要在目标开发板上运行，不是在编译主机上。

#### 编译时验证

**检查设备树编译**：
```bash
# 在项目根目录编译设备树
make ARCH=loongarch CROSS_COMPILE=loongarch64-linux-gnu- dtbs

# 检查编译是否成功
ls arch/loongarch/boot/dts/loongson/*.dtb
```

**检查内核配置**：
```bash
# 确认TPM SPI相关配置已启用
grep -E "CONFIG_TCG_TIS_SPI|CONFIG_SPI|CONFIG_SPI_MASTER" .config
```

#### 运行时验证（在开发板上）

**预期的系统文件路径**：
- `/sys/bus/spi/devices/` - SPI设备注册信息
- `/dev/tpm*` - TPM字符设备文件
- `/sys/class/tpm/tpm0/` - TPM设备属性
- `/proc/interrupts` - 系统中断统计

**验证命令**（在开发板上执行）：
```bash
# 检查SPI设备是否识别
ls /sys/bus/spi/devices/
# 预期输出：spi0.0 (表示spi0上的设备0)

# 检查TPM设备是否创建
ls /dev/tpm*
# 预期输出：/dev/tpm0 /dev/tpmrm0

# 查看内核启动日志中的TPM信息
dmesg | grep -i tpm
# 预期看到TPM设备注册成功的信息
```

### 常见问题和解决方法

#### 问题1：设备未识别
- **现象**：`/sys/bus/spi/devices/` 下没有TPM设备
- **原因**：设备树配置错误或SPI控制器未启用
- **解决**：检查设备树配置和SPI控制器状态

#### 问题2：通信超时
- **现象**：TPM操作超时，dmesg显示SPI传输错误
- **原因**：SPI频率过高或信号质量问题
- **解决**：降低`spi-max-frequency`或检查硬件连接

#### 问题3：中断不工作
- **现象**：TPM工作但响应慢，使用轮询模式
- **原因**：中断配置错误或GPIO配置问题
- **解决**：检查中断配置和GPIO设置

### 调试方法

**注意**：调试命令需要在目标开发板上执行，不是在编译主机上。

#### 编译时调试

**检查驱动是否编译**：
```bash
# 检查TPM SPI驱动是否编译进内核
find . -name "tpm_tis_spi.o" -o -name "tpm_tis_spi.ko"

# 检查LoongArch SPI驱动是否编译
find . -name "spi-ls-v1.o" -o -name "spi-ls-v1.ko"
```

**分析设备树编译结果**：
```bash
# 反编译设备树查看SPI配置
dtc -I dtb -O dts arch/loongarch/boot/dts/loongson/loongson64_2p500.dtb | grep -A10 -B5 spi
```

#### 运行时调试（在开发板上）

**预期的调试文件路径**：
- `/sys/kernel/debug/dynamic_debug/control` - 动态调试控制
- `/proc/interrupts` - 中断统计信息
- `/sys/class/spi_master/` - SPI主控制器信息
- `/sys/kernel/debug/gpio` - GPIO调试信息

**调试命令**（在开发板上执行）：
```bash
# 查看内核启动日志
dmesg | grep -E "(spi|tpm)"

# 检查SPI控制器是否注册
ls /sys/class/spi_master/

# 检查中断统计（如果配置了中断）
cat /proc/interrupts | grep -E "(spi|tpm|gpio)"

# 启用动态调试（如果支持）
echo 'module tpm_tis_spi +p' > /sys/kernel/debug/dynamic_debug/control
echo 'module spi_ls_v1 +p' > /sys/kernel/debug/dynamic_debug/control
```

## 总结

TPM SPI在此项目中的实现分为6个步骤：

1. **硬件连接** - 4根SPI信号线 + 中断线
2. **设备树配置** - 定义SPI控制器和TPM设备节点
3. **驱动注册** - tpm_tis_spi驱动注册到SPI总线
4. **设备探测** - probe函数初始化SPI物理层
5. **SPI通信** - 实现TPM专用的SPI传输协议
6. **集成TPM核心** - 通过操作函数表连接到TPM子系统

关键在于理解TPM SPI使用特殊的传输格式和等待机制，与标准SPI设备不同。

## 此项目中的SPI控制器详细说明

### LoongArch平台SPI控制器定义

在此Linux 4.19项目中，LoongArch架构定义了多个SPI控制器：

#### SPI控制器基址和定义位置

**SPI控制器定义文件**：`arch/loongarch/boot/dts/loongson/loongson64_2p500.dtsi`

```dts
// 文件：arch/loongarch/boot/dts/loongson/loongson64_2p500.dtsi
// 行号：359-368
spi1: spi@0x14203000 {
    compatible = "loongson,ls-spi-v1";
    status ="disabled";
    #address-cells = <1>;
    #size-cells = <0>;
    reg = <0 0x14203000 0 0x1000>;     // 基址: 0x14203000, 大小: 4KB
    spi-max-frequency = <50000000>;    // 最大50MHz
    interrupt-parent = <&icu>;
    interrupts = <6>;                  // 中断号6
};

// 文件：arch/loongarch/boot/dts/loongson/loongson64_2p500.dtsi
// 行号：370-374
spi2: spi@0x15109000 {
    compatible = "loongson,ls-spi-v1";
    status ="disabled";               // 默认禁用
    reg = <0 0x15109000 0 0x1000>;    // 基址: 0x15109000, 大小: 4KB
};

// 文件：arch/loongarch/boot/dts/loongson/loongson64_2p500.dtsi
// 行号：376-380
spi3: spi@0x15203000 {
    compatible = "loongson,ls-spi-v1";
    status ="disabled";               // 默认禁用
    reg = <0 0x15203000 0 0x1000>;    // 基址: 0x15203000, 大小: 4KB
};
```

**SPI0设备使用示例文件**：`arch/loongarch/boot/dts/loongson/loongson64_2p500.dts`

```dts
// 文件：arch/loongarch/boot/dts/loongson/loongson64_2p500.dts
// 行号：71-80
&spi0{
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi0_pin>;
    spidev@0 {
        compatible = "rohm,dh2228fv";
        spi-max-frequency = <100000000>;
        reg = <0>;
    };
};
```

#### SPI控制器基址总结表

| SPI控制器 | 物理基址 | 大小 | 中断号 | 默认状态 | 定义文件位置 |
|-----------|----------|------|--------|----------|-------------|
| **spi1** | `0x14203000` | 4KB | 6 | 禁用 | `loongson64_2p500.dtsi:359` |
| **spi2** | `0x15109000` | 4KB | - | 禁用 | `loongson64_2p500.dtsi:370` |
| **spi3** | `0x15203000` | 4KB | - | 禁用 | `loongson64_2p500.dtsi:376` |

**注意**：此项目中没有定义spi0节点，但在板级文件中通过`&spi0`引用使用。

#### 为什么选择spi0而不是spi1/spi2/spi3？

1. **板级配置**：spi0在板级文件中已配置为"okay"状态
2. **引脚配置**：spi0已配置了pinctrl引脚复用
3. **驱动兼容**：spi0经过充分测试，兼容性最好
4. **资源分配**：spi0通常预留给重要的安全设备如TPM

**重要说明**：实际上此项目中spi0的基址需要查看具体的SoC手册确定，因为dtsi文件中没有明确定义spi0节点。

### 配置参数选择指南

#### 1. SPI控制器选择原则

**基于项目代码分析的选择顺序**：
```bash
# 1. 查看板级文件中已启用的SPI控制器
grep -A10 "&spi" arch/loongarch/boot/dts/loongson/*.dts

# 2. 分析SPI控制器基址和状态
grep -A5 "spi@" arch/loongarch/boot/dts/loongson/loongson64_2p500.dtsi
```

**推荐选择顺序**：
1. **spi0** - 首选，在板级文件中已配置为"okay"
2. **spi2** - 次选，独立基址(0x15109000)，无冲突
3. **spi3** - 备选，独立基址(0x15203000)，无冲突
4. **spi1** - 不推荐，与其他控制器可能有基址冲突

### 基址定义文件位置详解

#### 主要定义文件

1. **设备树源文件**：
   - `arch/loongarch/boot/dts/loongson/loongson64_2p500.dtsi` - SPI控制器定义
   - `arch/loongarch/boot/dts/loongson/loongson64_2p500.dts` - 具体板级配置

2. **驱动源文件**：
   - `drivers/spi/spi-ls-v1.c` - LoongArch SPI v1驱动实现（推荐）
   - `drivers/spi/spi-ls.c` - LoongArch SPI旧版驱动
   - `drivers/char/tpm/tpm_tis_spi.c` - TPM SPI接口驱动

3. **头文件定义**：
   - SPI寄存器偏移定义在驱动文件中，无独立头文件
   - TPM相关定义：`drivers/char/tpm/tpm.h`

#### 设备树文件层次结构

```
arch/loongarch/boot/dts/loongson/
├── loongson64_2p500.dtsi          # SPI控制器基础定义
├── loongson64_2p500.dts           # 板级SPI配置
├── printer_2p300.dts              # 打印机板SPI配置
├── printer_2p500color.dts         # 彩色打印机板SPI配置
└── ...
```

#### 如何修改SPI基址（如果需要）

**步骤1：修改设备树定义**

**文件路径**：`arch/loongarch/boot/dts/loongson/loongson64_2p500.dtsi`

```bash
# 编辑基础设备树文件
vim arch/loongarch/boot/dts/loongson/loongson64_2p500.dtsi

# 修改SPI控制器基址（示例）
spi0: spi@0x14203000 {
    reg = <0 0x14203000 0 0x1000>;  # 修改这里的基址
};
```

**步骤2：重新编译设备树**
```bash
# 编译设备树
make dtbs

# 或者只编译特定的设备树
make arch/loongarch/boot/dts/loongson/loongson64_2p500.dtb
```

**步骤3：验证基址修改**
```bash
# 查看编译后的设备树
dtc -I dtb -O dts arch/loongarch/boot/dts/loongson/loongson64_2p500.dtb | grep -A5 spi@
```

### TPM SPI配置的最佳实践

#### 推荐配置（基于此项目）

**文件路径**：`arch/loongarch/boot/dts/loongson/your_board.dts`

```dts
&spi0 {                                    // 选择spi0
    status = "okay";                       // 启用SPI控制器
    pinctrl-names = "default";             // 引脚控制
    pinctrl-0 = <&spi0_pin>;              // 引脚配置引用

    tpm@0 {                               // TPM设备，使用片选0
        compatible = "tcg,tpm_tis-spi";   // 通用TPM SPI驱动
        reg = <0>;                        // 片选0（CS0）
        spi-max-frequency = <10000000>;   // 10MHz（保守设置）
        interrupt-parent = <&icu>;        // 中断控制器
        interrupts = <12 IRQ_TYPE_LEVEL_LOW>; // 中断配置
    };
};
```

#### 配置选择依据

1. **选择spi0的原因**：
   - 基址 `0x14203000` 是主SPI控制器
   - 默认启用状态，无需额外配置
   - 中断支持完整（中断号6）
   - 驱动兼容性最好

2. **基址的重要性**：
   - 基址决定了SPI控制器的物理位置
   - 错误的基址会导致设备无法访问
   - 基址冲突会导致系统不稳定

3. **中断配置的关联**：
   - SPI控制器中断号：6（来自ICU中断控制器）
   - TPM设备中断号：12（来自GPIO控制器）
   - 两者独立，不会冲突

#### 2. 片选号选择（reg = <0> vs reg = <1>）

**选择依据**：
- 硬件连接决定：TPM的CS引脚连接到SPI_CS0 → reg = <0>
- 硬件连接决定：TPM的CS引脚连接到SPI_CS1 → reg = <1>
- 通常TPM作为唯一SPI设备使用CS0

**基于项目分析**：
```bash
# 查看设备树中SPI控制器的片选配置
grep -A10 "spi@" arch/loongarch/boot/dts/loongson/loongson64_2p500.dtsi

# 查看板级文件中的片选使用情况
grep -A5 "reg = <" arch/loongarch/boot/dts/loongson/*.dts
```

#### 3. 频率选择（spi-max-frequency）
```dts
// 常见TPM芯片支持的频率：
spi-max-frequency = <5000000>;    // 5MHz - 保守选择，兼容性好
spi-max-frequency = <10000000>;   // 10MHz - 标准选择，大多数TPM支持
spi-max-frequency = <20000000>;   // 20MHz - 高性能，仅部分TPM支持

// 选择原则：
// 1. 从低频开始测试（5MHz）
// 2. 确认工作正常后逐步提高
// 3. 出现通信错误时降低频率
```

#### 4. GPIO和中断选择

**选择方法**：
1. 查看硬件原理图 - 确定TPM的IRQ引脚连接到哪个GPIO
2. 查看设备树中的GPIO控制器定义
3. 确认中断控制器的配置

**基于项目分析**：
```bash
# 查看GPIO控制器定义
grep -r "gpio" arch/loongarch/boot/dts/loongson/ | grep compatible

# 查看中断控制器定义
grep -r "interrupt-controller" arch/loongarch/boot/dts/loongson/

# 查看现有的中断配置示例
grep -r "interrupts = <" arch/loongarch/boot/dts/loongson/
```

#### 5. 中断类型选择
```dts
// 根据TPM芯片规格书选择：
interrupts = <12 IRQ_TYPE_LEVEL_LOW>;     // 低电平触发（最常见）
interrupts = <12 IRQ_TYPE_LEVEL_HIGH>;    // 高电平触发
interrupts = <12 IRQ_TYPE_EDGE_FALLING>;  // 下降沿触发
interrupts = <12 IRQ_TYPE_EDGE_RISING>;   // 上升沿触发

// 选择原则：
// 1. 查看TPM芯片数据手册
// 2. 大多数TPM使用低电平触发
// 3. 如果不确定，先尝试低电平触发
```

### 配置验证方法

#### 验证SPI控制器配置
```bash
# 检查SPI控制器是否启用
cat /sys/class/spi_master/spi0/device/uevent

# 检查SPI设备是否被识别
ls /sys/bus/spi/devices/
# 应该看到类似 spi0.0 的设备
```

#### 验证GPIO配置
```bash
# 检查GPIO是否可用
cat /sys/kernel/debug/gpio | grep -A5 -B5 "gpio-12"

# 测试中断是否工作
cat /proc/interrupts | grep tpm
```

#### 验证频率配置

**编译时验证**：
```bash
# 检查设备树中的频率配置
grep -r "spi-max-frequency" arch/loongarch/boot/dts/loongson/

# 反编译设备树查看最终配置
dtc -I dtb -O dts arch/loongarch/boot/dts/loongson/loongson64_2p500.dtb | grep -A5 -B5 "spi-max-frequency"
```

**运行时验证**（在开发板上）：
```bash
# 查看实际使用的频率
cat /sys/bus/spi/devices/spi0.0/max_speed_hz

# 查看内核日志中的SPI频率信息
dmesg | grep -i "spi.*frequency"
```
